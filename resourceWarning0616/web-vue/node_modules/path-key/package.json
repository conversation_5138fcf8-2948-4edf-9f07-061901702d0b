{"name": "path-key", "version": "2.0.1", "description": "Get the PATH environment variable key cross-platform", "license": "MIT", "repository": "sindresorhus/path-key", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["path", "key", "environment", "env", "variable", "var", "get", "cross-platform", "windows"], "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}}
{"version": 3, "file": "serialize.js", "sourceRoot": "", "sources": ["../../../lib/compile/jtd/serialize.ts"], "names": [], "mappings": ";;AAEA,mCAA0D;AAC1D,0BAAgD;AAChD,wCAAwE;AACxE,4CAA0C;AAC1C,oCAAwB;AACxB,kDAAqD;AACrD,oDAAiD;AACjD,kCAA+B;AAC/B,+CAAuC;AAEvC,MAAM,YAAY,GAAkD;IAClE,QAAQ,EAAE,iBAAiB;IAC3B,MAAM,EAAE,eAAe;IACvB,aAAa,EAAE,sBAAsB;IACrC,UAAU,EAAE,mBAAmB;IAC/B,kBAAkB,EAAE,mBAAmB;IACvC,IAAI,EAAE,eAAe;IACrB,IAAI,EAAE,aAAa;IACnB,GAAG,EAAE,YAAY;CAClB,CAAA;AAWD,SAAwB,iBAAiB,CAEvC,GAAc,EACd,WAA4B;IAE5B,MAAM,IAAI,GAAG,sBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IAC/C,IAAI,IAAI;QAAE,OAAO,IAAI,CAAA;IACrB,MAAM,EAAC,GAAG,EAAE,KAAK,EAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAA;IACnC,MAAM,EAAC,aAAa,EAAC,GAAG,IAAI,CAAC,IAAI,CAAA;IACjC,MAAM,GAAG,GAAG,IAAI,iBAAO,CAAC,IAAI,CAAC,KAAK,EAAE,EAAC,GAAG,EAAE,KAAK,EAAE,aAAa,EAAC,CAAC,CAAA;IAChE,MAAM,aAAa,GAAG,GAAG,CAAC,SAAS,CAAC,WAAW,CAAC,CAAA;IAChD,MAAM,GAAG,GAAiB;QACxB,IAAI,EAAE,IAAI;QACV,GAAG;QACH,MAAM,EAAE,GAAG,CAAC,MAAsB;QAClC,SAAS,EAAE,GAAG;QACd,WAAW;QACX,IAAI,EAAE,eAAC,CAAC,IAAI;KACb,CAAA;IAED,IAAI,UAA8B,CAAA;IAClC,IAAI,CAAC;QACH,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QAC3B,GAAG,CAAC,aAAa,GAAG,aAAa,CAAA;QACjC,GAAG,CAAC,IAAI,CAAC,aAAa,EAAE,eAAC,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE;YAC1C,GAAG,CAAC,GAAG,CAAC,eAAC,CAAC,IAAI,EAAE,IAAA,aAAG,EAAA,EAAE,CAAC,CAAA;YACtB,aAAa,CAAC,GAAG,CAAC,CAAA;YAClB,GAAG,CAAC,MAAM,CAAC,eAAC,CAAC,IAAI,CAAC,CAAA;QACpB,CAAC,CAAC,CAAA;QACF,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QACrC,MAAM,iBAAiB,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAA;QACxC,UAAU,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,eAAC,CAAC,KAAK,CAAC,UAAU,iBAAiB,EAAE,CAAA;QACnE,MAAM,aAAa,GAAG,IAAI,QAAQ,CAAC,GAAG,eAAC,CAAC,KAAK,EAAE,EAAE,UAAU,CAAC,CAAA;QAC5D,MAAM,SAAS,GAA8B,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAA;QAC5E,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,aAAa,EAAE,EAAC,GAAG,EAAE,SAAS,EAAC,CAAC,CAAA;QACjD,GAAG,CAAC,SAAS,GAAG,SAAS,CAAA;IAC3B,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,IAAI,UAAU;YAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4CAA4C,EAAE,UAAU,CAAC,CAAA;QAC3F,OAAO,GAAG,CAAC,SAAS,CAAA;QACpB,OAAO,GAAG,CAAC,aAAa,CAAA;QACxB,MAAM,CAAC,CAAA;IACT,CAAC;YAAS,CAAC;QACT,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;IAChC,CAAC;IACD,OAAO,GAAG,CAAA;AACZ,CAAC;AA7CD,oCA6CC;AAED,SAAS,aAAa,CAAC,GAAiB;IACtC,IAAI,IAAyB,CAAA;IAC7B,KAAK,MAAM,GAAG,IAAI,gBAAQ,EAAE,CAAC;QAC3B,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACtB,IAAI,GAAG,GAAG,CAAA;YACV,MAAK;QACP,CAAC;IACH,CAAC;IACD,iBAAiB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAA;AACpE,CAAC;AAED,SAAS,iBAAiB,CAAC,GAAiB,EAAE,aAA2C;IACvF,MAAM,EAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAC,GAAG,GAAG,CAAA;IAC/B,IAAI,CAAC,MAAM,CAAC,QAAQ;QAAE,OAAO,aAAa,CAAC,GAAG,CAAC,CAAA;IAC/C,GAAG,CAAC,EAAE,CACJ,IAAA,WAAC,EAAA,GAAG,IAAI,qBAAqB,IAAI,WAAW,EAC5C,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,eAAC,CAAC,IAAI,EAAE,IAAA,WAAC,EAAA,QAAQ,CAAC,EAChC,GAAG,EAAE,CAAC,aAAa,CAAC,GAAG,CAAC,CACzB,CAAA;AACH,CAAC;AAED,SAAS,iBAAiB,CAAC,GAAiB;IAC1C,MAAM,EAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAC,GAAG,GAAG,CAAA;IAC/B,GAAG,CAAC,GAAG,CAAC,eAAC,CAAC,IAAI,EAAE,IAAA,aAAG,EAAA,GAAG,CAAC,CAAA;IACvB,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;IACpC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE;QAC3B,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;QACpB,aAAa,CAAC,EAAC,GAAG,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,QAAQ,EAAE,IAAI,EAAE,EAAE,EAAC,CAAC,CAAA;IAC5D,CAAC,CAAC,CAAA;IACF,GAAG,CAAC,GAAG,CAAC,eAAC,CAAC,IAAI,EAAE,IAAA,aAAG,EAAA,GAAG,CAAC,CAAA;AACzB,CAAC;AAED,SAAS,eAAe,CAAC,GAAiB;IACxC,MAAM,EAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAC,GAAG,GAAG,CAAA;IAC/B,GAAG,CAAC,GAAG,CAAC,eAAC,CAAC,IAAI,EAAE,IAAA,aAAG,EAAA,GAAG,CAAC,CAAA;IACvB,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;IACpC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,iBAAiB,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAA;IAClF,GAAG,CAAC,GAAG,CAAC,eAAC,CAAC,IAAI,EAAE,IAAA,aAAG,EAAA,GAAG,CAAC,CAAA;AACzB,CAAC;AAED,SAAS,iBAAiB,CAAC,GAAiB,EAAE,GAAS,EAAE,MAAoB,EAAE,KAAY;IACzF,MAAM,EAAC,GAAG,EAAE,IAAI,EAAC,GAAG,GAAG,CAAA;IACvB,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;IACpB,eAAe,CAAC,EAAC,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,EAAC,CAAC,CAAA;IACpC,GAAG,CAAC,GAAG,CAAC,eAAC,CAAC,IAAI,EAAE,IAAA,aAAG,EAAA,GAAG,CAAC,CAAA;IACvB,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,EAAE,IAAA,WAAC,EAAA,GAAG,IAAI,GAAG,IAAA,qBAAW,EAAC,GAAG,CAAC,EAAE,CAAC,CAAA;IAC/D,aAAa,CAAC,EAAC,GAAG,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAA;AAC9C,CAAC;AAED,SAAS,sBAAsB,CAAC,GAAiB;IAC/C,MAAM,EAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAC,GAAG,GAAG,CAAA;IAC/B,MAAM,EAAC,aAAa,EAAC,GAAG,MAAM,CAAA;IAC9B,GAAG,CAAC,GAAG,CAAC,eAAC,CAAC,IAAI,EAAE,IAAA,aAAG,EAAA,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;IACxD,MAAM,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,IAAA,WAAC,EAAA,GAAG,IAAI,GAAG,IAAA,qBAAW,EAAC,aAAa,CAAC,EAAE,CAAC,CAAA;IACrE,eAAe,CAAC,EAAC,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,EAAC,CAAC,CAAA;IACpC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,CAAA;IACb,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;QACtC,GAAG,CAAC,MAAM,CAAC,IAAA,WAAC,EAAA,GAAG,GAAG,QAAQ,QAAQ,EAAE,CAAC,CAAA;QACrC,MAAM,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;QACpC,yBAAyB,CAAC,EAAC,GAAG,GAAG,EAAE,MAAM,EAAE,GAAG,EAAC,EAAE,aAAa,CAAC,CAAA;IACjE,CAAC;IACD,GAAG,CAAC,KAAK,EAAE,CAAA;IACX,GAAG,CAAC,GAAG,CAAC,eAAC,CAAC,IAAI,EAAE,IAAA,aAAG,EAAA,GAAG,CAAC,CAAA;AACzB,CAAC;AAED,SAAS,mBAAmB,CAAC,GAAiB;IAC5C,MAAM,EAAC,GAAG,EAAC,GAAG,GAAG,CAAA;IACjB,GAAG,CAAC,GAAG,CAAC,eAAC,CAAC,IAAI,EAAE,IAAA,aAAG,EAAA,GAAG,CAAC,CAAA;IACvB,yBAAyB,CAAC,GAAG,CAAC,CAAA;IAC9B,GAAG,CAAC,GAAG,CAAC,eAAC,CAAC,IAAI,EAAE,IAAA,aAAG,EAAA,GAAG,CAAC,CAAA;AACzB,CAAC;AAED,SAAS,yBAAyB,CAAC,GAAiB,EAAE,aAAsB;IAC1E,MAAM,EAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAC,GAAG,GAAG,CAAA;IAC/B,MAAM,EAAC,UAAU,EAAE,kBAAkB,EAAC,GAAG,MAAM,CAAA;IAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAA;IAC9B,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAA;IACzC,MAAM,QAAQ,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAA;IACtD,IAAI,KAAK,GAAG,CAAC,aAAa,CAAA;IAC1B,IAAI,SAA2B,CAAA;IAE/B,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE,CAAC;QACxB,IAAI,KAAK;YAAE,KAAK,GAAG,KAAK,CAAA;;YACnB,GAAG,CAAC,GAAG,CAAC,eAAC,CAAC,IAAI,EAAE,IAAA,aAAG,EAAA,GAAG,CAAC,CAAA;QAC5B,iBAAiB,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAA;IACxD,CAAC;IACD,IAAI,KAAK;QAAE,SAAS,GAAG,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;IAC7C,KAAK,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;QAC3B,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAA;QAC3B,GAAG,CAAC,EAAE,CAAC,IAAA,aAAG,EAAC,IAAA,WAAC,EAAA,GAAG,KAAK,gBAAgB,EAAE,IAAA,oBAAa,EAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE;YACzE,QAAQ,CAAC,GAAG,EAAE,SAAS,CAAC,CAAA;YACxB,iBAAiB,CAAC,GAAG,EAAE,kBAAkB,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAA;QACxD,CAAC,CAAC,CAAA;IACJ,CAAC;IACD,IAAI,MAAM,CAAC,oBAAoB,EAAE,CAAC;QAChC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,GAAG,EAAE,EAAE,CAC7B,GAAG,CAAC,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE,QAAQ,CAAC,EAAE,GAAG,EAAE,CAAC,iBAAiB,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,SAAS,CAAC,CAAC,CACtF,CAAA;IACH,CAAC;IAED,SAAS,IAAI,CAAC,EAAoB;QAChC,OAAO,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;IAClC,CAAC;IAED,SAAS,aAAa,CAAC,EAAY;QACjC,IAAI,aAAa;YAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;QACzC,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC;YACnC,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAA;QAC5E,CAAC;QACD,OAAO,EAAE,CAAA;IACX,CAAC;IAED,SAAS,QAAQ,CAAC,GAAW;QAC3B,OAAO,GAAG,CAAC,KAAK,CAAC,OAAO,EAAE,IAAA,WAAC,EAAA,GAAG,IAAI,GAAG,IAAA,qBAAW,EAAC,GAAG,CAAC,EAAE,CAAC,CAAA;IAC1D,CAAC;IAED,SAAS,iBAAiB,CAAC,GAAW,EAAE,UAAwB,EAAE,KAAW;QAC3E,GAAG,CAAC,GAAG,CAAC,eAAC,CAAC,IAAI,EAAE,IAAA,aAAG,EAAA,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QAC7C,aAAa,CAAC,EAAC,GAAG,GAAG,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAA;IAC1D,CAAC;IAED,SAAS,YAAY,CAAC,GAAS,EAAE,EAAY;QAC3C,OAAO,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,IAAA,aAAG,EAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAA,WAAC,EAAA,GAAG,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;IACrE,CAAC;AACH,CAAC;AAED,SAAS,aAAa,CAAC,GAAiB;IACtC,MAAM,EAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAC,GAAG,GAAG,CAAA;IAC/B,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;QACpB,KAAK,SAAS;YACZ,GAAG,CAAC,GAAG,CAAC,eAAC,CAAC,IAAI,EAAE,IAAA,WAAC,EAAA,GAAG,IAAI,qBAAqB,CAAC,CAAA;YAC9C,MAAK;QACP,KAAK,QAAQ;YACX,eAAe,CAAC,GAAG,CAAC,CAAA;YACpB,MAAK;QACP,KAAK,WAAW;YACd,GAAG,CAAC,EAAE,CACJ,IAAA,WAAC,EAAA,GAAG,IAAI,kBAAkB,EAC1B,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,eAAC,CAAC,IAAI,EAAE,IAAA,WAAC,EAAA,SAAS,IAAI,sBAAsB,CAAC,EAC3D,GAAG,EAAE,CAAC,eAAe,CAAC,GAAG,CAAC,CAC3B,CAAA;YACD,MAAK;QACP;YACE,eAAe,CAAC,GAAG,CAAC,CAAA;IACxB,CAAC;AACH,CAAC;AAED,SAAS,eAAe,CAAC,EAAC,GAAG,EAAE,IAAI,EAAe;IAChD,GAAG,CAAC,GAAG,CAAC,eAAC,CAAC,IAAI,EAAE,IAAA,WAAC,EAAA,GAAG,IAAA,cAAO,EAAC,GAAG,EAAE,eAAK,CAAC,IAAI,IAAI,GAAG,CAAC,CAAA;AACrD,CAAC;AAED,SAAS,eAAe,CAAC,EAAC,GAAG,EAAE,IAAI,EAAe;IAChD,GAAG,CAAC,GAAG,CAAC,eAAC,CAAC,IAAI,EAAE,IAAA,WAAC,EAAA,QAAQ,IAAI,EAAE,CAAC,CAAA;AAClC,CAAC;AAED,SAAS,YAAY,CAAC,GAAiB;IACrC,MAAM,EAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,SAAS,EAAC,GAAG,GAAG,CAAA;IAC7D,MAAM,EAAC,GAAG,EAAC,GAAG,MAAM,CAAA;IACpB,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,CAAC,CAAA;IAClC,IAAI,CAAC,SAAS;QAAE,MAAM,IAAI,mBAAe,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,EAAE,GAAG,EAAE,iBAAiB,GAAG,EAAE,CAAC,CAAA;IACjG,IAAI,CAAC,IAAA,YAAM,EAAC,SAAS,CAAC;QAAE,OAAO,aAAa,CAAC,EAAC,GAAG,GAAG,EAAE,MAAM,EAAE,SAAS,EAAC,CAAC,CAAA;IACzE,MAAM,EAAC,IAAI,EAAC,GAAG,SAAS,CAAA;IACxB,MAAM,GAAG,GAAG,iBAAiB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,aAAS,CAAC,EAAC,MAAM,EAAE,SAAS,EAAE,IAAI,EAAC,CAAC,EAAE,WAAW,CAAC,CAAA;IAC/F,GAAG,CAAC,GAAG,CAAC,eAAC,CAAC,IAAI,EAAE,IAAA,WAAC,EAAA,GAAG,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,CAAA;AACxD,CAAC;AAED,SAAS,YAAY,CAAC,GAAY,EAAE,GAAc;IAChD,OAAO,GAAG,CAAC,SAAS;QAClB,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,WAAW,EAAE,EAAC,GAAG,EAAE,GAAG,CAAC,SAAS,EAAC,CAAC;QACnD,CAAC,CAAC,IAAA,WAAC,EAAA,GAAG,GAAG,CAAC,UAAU,CAAC,SAAS,EAAE,EAAC,GAAG,EAAE,GAAG,EAAC,CAAC,YAAY,CAAA;AAC3D,CAAC;AAED,SAAS,cAAc,CAAC,EAAC,GAAG,EAAE,IAAI,EAAe;IAC/C,GAAG,CAAC,GAAG,CAAC,eAAC,CAAC,IAAI,EAAE,IAAA,WAAC,EAAA,kBAAkB,IAAI,GAAG,CAAC,CAAA;AAC7C,CAAC;AAED,SAAS,QAAQ,CAAC,EAAC,GAAG,EAAe,EAAE,KAAY;IACjD,IAAI,KAAK,EAAE,CAAC;QACV,GAAG,CAAC,EAAE,CACJ,KAAK,EACL,GAAG,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,EAC9B,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,eAAC,CAAC,IAAI,EAAE,IAAA,aAAG,EAAA,GAAG,CAAC,CAC9B,CAAA;IACH,CAAC;SAAM,CAAC;QACN,GAAG,CAAC,GAAG,CAAC,eAAC,CAAC,IAAI,EAAE,IAAA,aAAG,EAAA,GAAG,CAAC,CAAA;IACzB,CAAC;AACH,CAAC"}
{"name": "prettier", "version": "2.8.8", "description": "<PERSON><PERSON><PERSON> is an opinionated code formatter", "bin": "./bin-prettier.js", "repository": "prettier/prettier", "funding": "https://github.com/prettier/prettier?sponsor=1", "homepage": "https://prettier.io", "author": "<PERSON>", "license": "MIT", "main": "./index.js", "browser": "./standalone.js", "unpkg": "./standalone.js", "engines": {"node": ">=10.13.0"}, "files": ["*.js", "esm/*.mjs"]}
{"version": 3, "file": "node.d.ts", "sourceRoot": "", "sources": ["../src/node.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAqB,MAAM,gBAAgB,CAAC;AAahE,UAAU,kBAAkB;IACxB,mDAAmD;IACnD,SAAS,EAAE,MAAM,CAAC;IAClB,qDAAqD;IACrD,QAAQ,EAAE,MAAM,CAAC;IACjB,wCAAwC;IACxC,WAAW,EAAE,MAAM,CAAC;IACpB,kDAAkD;IAClD,OAAO,EAAE,MAAM,CAAC;IAChB,gGAAgG;IAChG,MAAM,EAAE,MAAM,CAAC;IACf,mFAAmF;IACnF,SAAS,EAAE,MAAM,CAAC;CACrB;AAED,UAAU,qBAAsB,SAAQ,kBAAkB;IACtD,QAAQ,CAAC,EAAE,kBAAkB,CAAC;IAC9B,MAAM,CAAC,EAAE,kBAAkB,CAAC;CAC/B;AAED;;;GAGG;AACH,qBAAa,IAAI;IA2BM,IAAI,EAAE,WAAW;IA1BpC,yBAAyB;IACzB,MAAM,EAAE,gBAAgB,GAAG,IAAI,CAAQ;IAEvC,uBAAuB;IACvB,IAAI,EAAE,IAAI,GAAG,IAAI,CAAQ;IAEzB,mBAAmB;IACnB,IAAI,EAAE,IAAI,GAAG,IAAI,CAAQ;IAEzB,2FAA2F;IAC3F,UAAU,EAAE,MAAM,GAAG,IAAI,CAAQ;IAEjC,uFAAuF;IACvF,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAQ;IAE/B;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,kBAAkB,GAAG,IAAI,CAAC;IAE/C;;;OAGG;gBACgB,IAAI,EAAE,WAAW;IAIpC;;;OAGG;IACH,IAAI,QAAQ,IAAI,MAAM,CAErB;IAID;;;OAGG;IACH,IAAI,UAAU,IAAI,gBAAgB,GAAG,IAAI,CAExC;IAED,IAAI,UAAU,CAAC,MAAM,EAAE,gBAAgB,GAAG,IAAI,EAE7C;IAED;;;OAGG;IACH,IAAI,eAAe,IAAI,IAAI,GAAG,IAAI,CAEjC;IAED,IAAI,eAAe,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,EAEpC;IAED;;;OAGG;IACH,IAAI,WAAW,IAAI,IAAI,GAAG,IAAI,CAE7B;IAED,IAAI,WAAW,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,EAEhC;IAED;;;;;OAKG;IACH,SAAS,CAAC,CAAC,SAAS,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,SAAS,UAAQ,GAAG,CAAC;CAG3D;AAED;;GAEG;AACH,qBAAa,QAAS,SAAQ,IAAI;IAOnB,IAAI,EAAE,MAAM;IANvB;;;OAGG;gBAEC,IAAI,EAAE,WAAW,CAAC,OAAO,GAAG,WAAW,CAAC,IAAI,GAAG,WAAW,CAAC,SAAS,EAC7D,IAAI,EAAE,MAAM;IAKvB;;;OAGG;IACH,IAAI,SAAS,IAAI,MAAM,CAEtB;IAED,IAAI,SAAS,CAAC,IAAI,EAAE,MAAM,EAEzB;CACJ;AAED;;GAEG;AACH,qBAAa,IAAK,SAAQ,QAAQ;gBAClB,IAAI,EAAE,MAAM;CAG3B;AAED;;GAEG;AACH,qBAAa,OAAQ,SAAQ,QAAQ;gBACrB,IAAI,EAAE,MAAM;CAG3B;AAED;;GAEG;AACH,qBAAa,qBAAsB,SAAQ,QAAQ;IAC5B,IAAI,EAAE,MAAM;gBAAZ,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM;IAI7C,kEAAkE;IAClE,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,+EAA+E;IAC/E,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,+EAA+E;IAC/E,YAAY,CAAC,EAAE,MAAM,CAAC;CACzB;AAED;;GAEG;AACH,qBAAa,gBAAiB,SAAQ,IAAI;IAY3B,QAAQ,EAAE,IAAI,EAAE;IAX3B;;;OAGG;gBAEC,IAAI,EACE,WAAW,CAAC,IAAI,GAChB,WAAW,CAAC,KAAK,GACjB,WAAW,CAAC,MAAM,GAClB,WAAW,CAAC,KAAK,GACjB,WAAW,CAAC,GAAG,EACd,QAAQ,EAAE,IAAI,EAAE;IAM3B,+BAA+B;IAC/B,IAAI,UAAU,IAAI,IAAI,GAAG,IAAI,CAE5B;IAED,8BAA8B;IAC9B,IAAI,SAAS,IAAI,IAAI,GAAG,IAAI,CAI3B;IAED;;;OAGG;IACH,IAAI,UAAU,IAAI,IAAI,EAAE,CAEvB;IAED,IAAI,UAAU,CAAC,QAAQ,EAAE,IAAI,EAAE,EAE9B;CACJ;AAED;;GAEG;AACH,qBAAa,QAAS,SAAQ,gBAAgB;gBAC9B,QAAQ,EAAE,IAAI,EAAE;IAI5B,mGAAmG;IACnG,QAAQ,CAAC,EAAE,WAAW,GAAG,QAAQ,GAAG,gBAAgB,CAAC;CACxD;AAED;;GAEG;AACH,UAAU,SAAS;IACf,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,EAAE,MAAM,CAAC;IACd,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,MAAM,CAAC,EAAE,MAAM,CAAC;CACnB;AAED;;GAEG;AACH,qBAAa,OAAQ,SAAQ,gBAAgB;IAO9B,IAAI,EAAE,MAAM;IACZ,OAAO,EAAE;QAAE,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE;IAP9C;;;;OAIG;gBAEQ,IAAI,EAAE,MAAM,EACZ,OAAO,EAAE;QAAE,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,EAC1C,QAAQ,GAAE,IAAI,EAAO,EACrB,IAAI,GACE,WAAW,CAAC,GAAG,GACf,WAAW,CAAC,MAAM,GAClB,WAAW,CAAC,KAIG;IAKzB;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,qBAAqB,GAAG,IAAI,CAAC;IAIlD;;;OAGG;IACH,IAAI,OAAO,IAAI,MAAM,CAEpB;IAED,IAAI,OAAO,CAAC,IAAI,EAAE,MAAM,EAEvB;IAED,IAAI,UAAU,IAAI,SAAS,EAAE,CAO5B;IAED,uCAAuC;IACvC,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,kDAAkD;IAClD,oBAAoB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC9C,kEAAkE;IAClE,iBAAiB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;CAC9C;AAED;;;GAGG;AACH,wBAAgB,KAAK,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,IAAI,OAAO,CAEjD;AAED;;;GAGG;AACH,wBAAgB,OAAO,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,IAAI,gBAAgB,CAE5D;AAED;;;GAGG;AACH,wBAAgB,MAAM,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,IAAI,IAAI,CAE/C;AAED;;;GAGG;AACH,wBAAgB,SAAS,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,IAAI,QAAQ,CAEtD;AAED;;;GAGG;AACH,wBAAgB,WAAW,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,IAAI,qBAAqB,CAErE;AAED;;;GAGG;AACH,wBAAgB,UAAU,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,IAAI,QAAQ,CAEvD;AAED;;;GAGG;AACH,wBAAgB,WAAW,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,IAAI,gBAAgB,CAEhE;AAED;;;;;GAKG;AACH,wBAAgB,SAAS,CAAC,CAAC,SAAS,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,SAAS,UAAQ,GAAG,CAAC,CA4DvE"}
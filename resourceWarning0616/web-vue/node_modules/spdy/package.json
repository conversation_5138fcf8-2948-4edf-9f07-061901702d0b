{"name": "spdy", "version": "4.0.2", "description": "Implementation of the SPDY protocol on node.js.", "license": "MIT", "scripts": {"lint": "standard", "test": "mocha --reporter=spec test/*-test.js", "coverage": "istanbul cover node_modules/.bin/_mocha -- --reporter=spec test/**/*-test.js"}, "pre-commit": ["lint", "test"], "keywords": ["spdy"], "repository": {"type": "git", "url": "git://github.com/indutny/node-spdy.git"}, "homepage": "https://github.com/indutny/node-spdy", "bugs": {"email": "<EMAIL>", "url": "https://github.com/spdy-http2/node-spdy/issues"}, "author": "<PERSON><PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> <<EMAIL>>"], "dependencies": {"debug": "^4.1.0", "handle-thing": "^2.0.0", "http-deceiver": "^1.2.7", "select-hose": "^2.0.0", "spdy-transport": "^3.0.0"}, "devDependencies": {"istanbul": "^0.4.5", "mocha": "^6.2.3", "pre-commit": "^1.2.2", "standard": "^13.1.0"}, "engines": {"node": ">=6.0.0"}, "main": "./lib/spdy", "optionalDependencies": {}}
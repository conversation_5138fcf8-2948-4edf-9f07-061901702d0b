1.52.0 / 2022-02-21
===================

  * Add extensions from IANA for more `image/*` types
  * Add extension `.asc` to `application/pgp-keys`
  * Add extensions to various XML types
  * Add new upstream MIME types

1.51.0 / 2021-11-08
===================

  * Add new upstream MIME types
  * Mark `image/vnd.microsoft.icon` as compressible
  * Mark `image/vnd.ms-dds` as compressible

1.50.0 / 2021-09-15
===================

  * Add deprecated iWorks mime types and extensions
  * Add new upstream MIME types

1.49.0 / 2021-07-26
===================

  * Add extension `.trig` to `application/trig`
  * Add new upstream MIME types

1.48.0 / 2021-05-30
===================

  * Add extension `.mvt` to `application/vnd.mapbox-vector-tile`
  * Add new upstream MIME types
  * Mark `text/yaml` as compressible

1.47.0 / 2021-04-01
===================

  * Add new upstream MIME types
  * Remove ambigious extensions from IANA for `application/*+xml` types
  * Update primary extension to `.es` for `application/ecmascript`

1.46.0 / 2021-02-13
===================

  * Add extension `.amr` to `audio/amr`
  * Add extension `.m4s` to `video/iso.segment`
  * Add extension `.opus` to `audio/ogg`
  * Add new upstream MIME types

1.45.0 / 2020-09-22
===================

  * Add `application/ubjson` with extension `.ubj`
  * Add `image/avif` with extension `.avif`
  * Add `image/ktx2` with extension `.ktx2`
  * Add extension `.dbf` to `application/vnd.dbf`
  * Add extension `.rar` to `application/vnd.rar`
  * Add extension `.td` to `application/urc-targetdesc+xml`
  * Add new upstream MIME types
  * Fix extension of `application/vnd.apple.keynote` to be `.key`

1.44.0 / 2020-04-22
===================

  * Add charsets from IANA
  * Add extension `.cjs` to `application/node`
  * Add new upstream MIME types

1.43.0 / 2020-01-05
===================

  * Add `application/x-keepass2` with extension `.kdbx`
  * Add extension `.mxmf` to `audio/mobile-xmf`
  * Add extensions from IANA for `application/*+xml` types
  * Add new upstream MIME types

1.42.0 / 2019-09-25
===================

  * Add `image/vnd.ms-dds` with extension `.dds`
  * Add new upstream MIME types
  * Remove compressible from `multipart/mixed`

1.41.0 / 2019-08-30
===================

  * Add new upstream MIME types
  * Add `application/toml` with extension `.toml`
  * Mark `font/ttf` as compressible

1.40.0 / 2019-04-20
===================

  * Add extensions from IANA for `model/*` types
  * Add `text/mdx` with extension `.mdx`

1.39.0 / 2019-04-04
===================

  * Add extensions `.siv` and `.sieve` to `application/sieve`
  * Add new upstream MIME types

1.38.0 / 2019-02-04
===================

  * Add extension `.nq` to `application/n-quads`
  * Add extension `.nt` to `application/n-triples`
  * Add new upstream MIME types
  * Mark `text/less` as compressible

1.37.0 / 2018-10-19
===================

  * Add extensions to HEIC image types
  * Add new upstream MIME types

1.36.0 / 2018-08-20
===================

  * Add Apple file extensions from IANA
  * Add extensions from IANA for `image/*` types
  * Add new upstream MIME types

1.35.0 / 2018-07-15
===================

  * Add extension `.owl` to `application/rdf+xml`
  * Add new upstream MIME types
    - Removes extension `.woff` from `application/font-woff`

1.34.0 / 2018-06-03
===================

  * Add extension `.csl` to `application/vnd.citationstyles.style+xml`
  * Add extension `.es` to `application/ecmascript`
  * Add new upstream MIME types
  * Add `UTF-8` as default charset for `text/turtle`
  * Mark all XML-derived types as compressible

1.33.0 / 2018-02-15
===================

  * Add extensions from IANA for `message/*` types
  * Add new upstream MIME types
  * Fix some incorrect OOXML types
  * Remove `application/font-woff2`

1.32.0 / 2017-11-29
===================

  * Add new upstream MIME types
  * Update `text/hjson` to registered `application/hjson`
  * Add `text/shex` with extension `.shex`

1.31.0 / 2017-10-25
===================

  * Add `application/raml+yaml` with extension `.raml`
  * Add `application/wasm` with extension `.wasm`
  * Add new `font` type from IANA
  * Add new upstream font extensions
  * Add new upstream MIME types
  * Add extensions for JPEG-2000 images

1.30.0 / 2017-08-27
===================

  * Add `application/vnd.ms-outlook`
  * Add `application/x-arj`
  * Add extension `.mjs` to `application/javascript`
  * Add glTF types and extensions
  * Add new upstream MIME types
  * Add `text/x-org`
  * Add VirtualBox MIME types
  * Fix `source` records for `video/*` types that are IANA
  * Update `font/opentype` to registered `font/otf`

1.29.0 / 2017-07-10
===================

  * Add `application/fido.trusted-apps+json`
  * Add extension `.wadl` to `application/vnd.sun.wadl+xml`
  * Add new upstream MIME types
  * Add `UTF-8` as default charset for `text/css`

1.28.0 / 2017-05-14
===================

  * Add new upstream MIME types
  * Add extension `.gz` to `application/gzip`
  * Update extensions `.md` and `.markdown` to be `text/markdown`

1.27.0 / 2017-03-16
===================

  * Add new upstream MIME types
  * Add `image/apng` with extension `.apng`

1.26.0 / 2017-01-14
===================

  * Add new upstream MIME types
  * Add extension `.geojson` to `application/geo+json`

1.25.0 / 2016-11-11
===================

  * Add new upstream MIME types

1.24.0 / 2016-09-18
===================

  * Add `audio/mp3`
  * Add new upstream MIME types

1.23.0 / 2016-05-01
===================

  * Add new upstream MIME types
  * Add extension `.3gpp` to `audio/3gpp`

1.22.0 / 2016-02-15
===================

  * Add `text/slim`
  * Add extension `.rng` to `application/xml`
  * Add new upstream MIME types
  * Fix extension of `application/dash+xml` to be `.mpd`
  * Update primary extension to `.m4a` for `audio/mp4`

1.21.0 / 2016-01-06
===================

  * Add Google document types
  * Add new upstream MIME types

1.20.0 / 2015-11-10
===================

  * Add `text/x-suse-ymp`
  * Add new upstream MIME types

1.19.0 / 2015-09-17
===================

  * Add `application/vnd.apple.pkpass`
  * Add new upstream MIME types

1.18.0 / 2015-09-03
===================

  * Add new upstream MIME types

1.17.0 / 2015-08-13
===================

  * Add `application/x-msdos-program`
  * Add `audio/g711-0`
  * Add `image/vnd.mozilla.apng`
  * Add extension `.exe` to `application/x-msdos-program`

1.16.0 / 2015-07-29
===================

  * Add `application/vnd.uri-map`

1.15.0 / 2015-07-13
===================

  * Add `application/x-httpd-php`

1.14.0 / 2015-06-25
===================

  * Add `application/scim+json`
  * Add `application/vnd.3gpp.ussd+xml`
  * Add `application/vnd.biopax.rdf+xml`
  * Add `text/x-processing`

1.13.0 / 2015-06-07
===================

  * Add nginx as a source
  * Add `application/x-cocoa`
  * Add `application/x-java-archive-diff`
  * Add `application/x-makeself`
  * Add `application/x-perl`
  * Add `application/x-pilot`
  * Add `application/x-redhat-package-manager`
  * Add `application/x-sea`
  * Add `audio/x-m4a`
  * Add `audio/x-realaudio`
  * Add `image/x-jng`
  * Add `text/mathml`

1.12.0 / 2015-06-05
===================

  * Add `application/bdoc`
  * Add `application/vnd.hyperdrive+json`
  * Add `application/x-bdoc`
  * Add extension `.rtf` to `text/rtf`

1.11.0 / 2015-05-31
===================

  * Add `audio/wav`
  * Add `audio/wave`
  * Add extension `.litcoffee` to `text/coffeescript`
  * Add extension `.sfd-hdstx` to `application/vnd.hydrostatix.sof-data`
  * Add extension `.n-gage` to `application/vnd.nokia.n-gage.symbian.install`

1.10.0 / 2015-05-19
===================

  * Add `application/vnd.balsamiq.bmpr`
  * Add `application/vnd.microsoft.portable-executable`
  * Add `application/x-ns-proxy-autoconfig`

1.9.1 / 2015-04-19
==================

  * Remove `.json` extension from `application/manifest+json`
    - This is causing bugs downstream

1.9.0 / 2015-04-19
==================

  * Add `application/manifest+json`
  * Add `application/vnd.micro+json`
  * Add `image/vnd.zbrush.pcx`
  * Add `image/x-ms-bmp`

1.8.0 / 2015-03-13
==================

  * Add `application/vnd.citationstyles.style+xml`
  * Add `application/vnd.fastcopy-disk-image`
  * Add `application/vnd.gov.sk.xmldatacontainer+xml`
  * Add extension `.jsonld` to `application/ld+json`

1.7.0 / 2015-02-08
==================

  * Add `application/vnd.gerber`
  * Add `application/vnd.msa-disk-image`

1.6.1 / 2015-02-05
==================

  * Community extensions ownership transferred from `node-mime`

1.6.0 / 2015-01-29
==================

  * Add `application/jose`
  * Add `application/jose+json`
  * Add `application/json-seq`
  * Add `application/jwk+json`
  * Add `application/jwk-set+json`
  * Add `application/jwt`
  * Add `application/rdap+json`
  * Add `application/vnd.gov.sk.e-form+xml`
  * Add `application/vnd.ims.imsccv1p3`

1.5.0 / 2014-12-30
==================

  * Add `application/vnd.oracle.resource+json`
  * Fix various invalid MIME type entries
    - `application/mbox+xml`
    - `application/oscp-response`
    - `application/vwg-multiplexed`
    - `audio/g721`

1.4.0 / 2014-12-21
==================

  * Add `application/vnd.ims.imsccv1p2`
  * Fix various invalid MIME type entries
    - `application/vnd-acucobol`
    - `application/vnd-curl`
    - `application/vnd-dart`
    - `application/vnd-dxr`
    - `application/vnd-fdf`
    - `application/vnd-mif`
    - `application/vnd-sema`
    - `application/vnd-wap-wmlc`
    - `application/vnd.adobe.flash-movie`
    - `application/vnd.dece-zip`
    - `application/vnd.dvb_service`
    - `application/vnd.micrografx-igx`
    - `application/vnd.sealed-doc`
    - `application/vnd.sealed-eml`
    - `application/vnd.sealed-mht`
    - `application/vnd.sealed-ppt`
    - `application/vnd.sealed-tiff`
    - `application/vnd.sealed-xls`
    - `application/vnd.sealedmedia.softseal-html`
    - `application/vnd.sealedmedia.softseal-pdf`
    - `application/vnd.wap-slc`
    - `application/vnd.wap-wbxml`
    - `audio/vnd.sealedmedia.softseal-mpeg`
    - `image/vnd-djvu`
    - `image/vnd-svf`
    - `image/vnd-wap-wbmp`
    - `image/vnd.sealed-png`
    - `image/vnd.sealedmedia.softseal-gif`
    - `image/vnd.sealedmedia.softseal-jpg`
    - `model/vnd-dwf`
    - `model/vnd.parasolid.transmit-binary`
    - `model/vnd.parasolid.transmit-text`
    - `text/vnd-a`
    - `text/vnd-curl`
    - `text/vnd.wap-wml`
  * Remove example template MIME types
    - `application/example`
    - `audio/example`
    - `image/example`
    - `message/example`
    - `model/example`
    - `multipart/example`
    - `text/example`
    - `video/example`

1.3.1 / 2014-12-16
==================

  * Fix missing extensions
    - `application/json5`
    - `text/hjson`

1.3.0 / 2014-12-07
==================

  * Add `application/a2l`
  * Add `application/aml`
  * Add `application/atfx`
  * Add `application/atxml`
  * Add `application/cdfx+xml`
  * Add `application/dii`
  * Add `application/json5`
  * Add `application/lxf`
  * Add `application/mf4`
  * Add `application/vnd.apache.thrift.compact`
  * Add `application/vnd.apache.thrift.json`
  * Add `application/vnd.coffeescript`
  * Add `application/vnd.enphase.envoy`
  * Add `application/vnd.ims.imsccv1p1`
  * Add `text/csv-schema`
  * Add `text/hjson`
  * Add `text/markdown`
  * Add `text/yaml`

1.2.0 / 2014-11-09
==================

  * Add `application/cea`
  * Add `application/dit`
  * Add `application/vnd.gov.sk.e-form+zip`
  * Add `application/vnd.tmd.mediaflex.api+xml`
  * Type `application/epub+zip` is now IANA-registered

1.1.2 / 2014-10-23
==================

  * Rebuild database for `application/x-www-form-urlencoded` change

1.1.1 / 2014-10-20
==================

  * Mark `application/x-www-form-urlencoded` as compressible.

1.1.0 / 2014-09-28
==================

  * Add `application/font-woff2`

1.0.3 / 2014-09-25
==================

  * Fix engine requirement in package

1.0.2 / 2014-09-25
==================

  * Add `application/coap-group+json`
  * Add `application/dcd`
  * Add `application/vnd.apache.thrift.binary`
  * Add `image/vnd.tencent.tap`
  * Mark all JSON-derived types as compressible
  * Update `text/vtt` data

1.0.1 / 2014-08-30
==================

  * Fix extension ordering

1.0.0 / 2014-08-30
==================

  * Add `application/atf`
  * Add `application/merge-patch+json`
  * Add `multipart/x-mixed-replace`
  * Add `source: 'apache'` metadata
  * Add `source: 'iana'` metadata
  * Remove badly-assumed charset data

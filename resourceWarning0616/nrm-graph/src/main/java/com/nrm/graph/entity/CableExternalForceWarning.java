package com.nrm.graph.entity;

import javax.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 光缆外力点隐患预警实体
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Entity
@Table(name = "cable_external_force_warning")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CableExternalForceWarning {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 监测点名称
     */
    @Column(name = "point_name", nullable = false, length = 200)
    private String pointName;
    
    /**
     * 点位编号
     */
    @Column(name = "point_code", nullable = false, length = 100)
    private String pointCode;
    
    /**
     * 关联光缆名称
     */
    @Column(name = "cable_name", length = 200)
    private String cableName;
    
    /**
     * 地理位置
     */
    @Column(name = "location", length = 500)
    private String location;
    
    /**
     * 经度
     */
    @Column(name = "longitude")
    private Double longitude;
    
    /**
     * 纬度
     */
    @Column(name = "latitude")
    private Double latitude;
    
    /**
     * 外力类型：construction-施工挖掘，vehicle-车辆碾压，natural-自然灾害，human-人为破坏
     */
    @Column(name = "force_type", length = 50)
    private String forceType;
    
    /**
     * 风险等级：safe-安全，low-低风险，medium-中风险，high-高风险
     */
    @Column(name = "risk_level", length = 20)
    private String riskLevel;
    
    /**
     * 风险评分
     */
    @Column(name = "risk_score")
    private Integer riskScore;
    
    /**
     * 处理状态：pending-待处理，processing-处理中，resolved-已解决
     */
    @Column(name = "status", length = 20)
    private String status;
    
    /**
     * 最后检测时间
     */
    @Column(name = "last_detect_time")
    private LocalDateTime lastDetectTime;
    
    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;
    
    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;
    
    @PrePersist
    protected void onCreate() {
        createTime = LocalDateTime.now();
        updateTime = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updateTime = LocalDateTime.now();
    }
}
